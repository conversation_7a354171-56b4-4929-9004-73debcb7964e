.om-floating-action-bar-wrapper
  position: relative
  overflow: visible !important // Ensure outlines aren't cut off
  z-index: 99999 !important // Ensure high z-index for the entire wrapper

  .om-floating-action-bar-action
    position: absolute
    background: #8444E1
    border-top-left-radius: 4px
    border-top-right-radius: 4px
    height: 28px
    width: 36px
    top: -35px
    right: -7px
    cursor: pointer
    z-index: 99999
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) // Add shadow for better visibility

    svg
      display: flex
      height: 100%
      margin-left: auto
      margin-right: auto
      path
        fill: white
        transition: 0.15s all

  &:hover
    svg
      path
        fill: #E3E5E8

// Ensure parent containers don't clip the floating action bar
.om-floating-action-bar-content
  position: relative
  overflow: visible !important
  z-index: 99999 !important // Ensure high z-index

// Force all parent elements to have visible overflow
html body .om-web-selector-outline-show
  &, & *
    overflow: visible !important

  // Use outline property instead of pseudo-element
  outline: 5px solid #8444E1 !important
  outline-offset: 2px !important
  z-index: 99998 !important
