import { ChangeBuilder } from '@om/change-builder';
import BaseAction from './BaseAction';
import { DOM } from './helper/DOM';
import { generateRandomString } from '../../helper';
import TYPES from './helper/types';
import { CHANGE_TYPE_ATTRIBUTE_KEY } from '../../constants';
import InsertHTML from './InsertHTML';

export default class SmartProductTag extends InsertHTML {
  static createChange(selector, productTag, variableName) {
    return {
      id: generateRandomString(10),
      selector,
      position: 'beforebegin',
      settingVariable: variableName,
      content: productTag,
      tag: 'DIV',
      classes: [],
      style: {
        desktop: {},
        mobile: {
          breakpoint: 640,
        },
        customCSS: '',
      },
      type: TYPES.SMART_PRODUCT_TAG,
    };
  }

  static applyChange({ change }) {
    DOM.removeElementById(change.id);
    DOM.removeScriptTags(change.id);
    DOM.removeStyleTags(change.id);

    let element = DOM.getElement(change.selector);
    if (!element && change.alternativeSelectors) {
      element = DOM.getElement(change.alternativeSelectors[0]);
    }

    if (element) {
      const built = ChangeBuilder.build(change);
      console.log('@@@@@css?', built);
      // TODO[fopi]
      element.insertAdjacentHTML(change.position, built.html);
      const newElement = DOM.getElementById(change.id);
      this.setUniqueAttributes(newElement, change);
      DOM.createStyleTag(change.id, built.css);
    }

    return change;
  }

  static dataToSave(change) {
    const baseData = BaseAction.dataToSave(change);

    baseData.content = undefined;
    baseData.settingVariable = change.settingVariable;

    return baseData;
  }

  static activateHighlight({ change }) {
    const element = DOM.getElementById(change.id);
    if (!element) return;

    this.setUniqueAttributes(element, change);
    DOM.addClass(element, this.getHighlightClass());
  }

  static toggleChanges({ change }) {
    const element = DOM.getElementById(change.id);
    if (!element) return;

    this.setUniqueAttributes(element, change);
    const isShowing = DOM.getClassList(element).contains(this.getHighlightClass());
    if (isShowing) {
      DOM.removeClass(element, this.getHighlightClass());
    } else {
      DOM.addClass(element, this.getHighlightClass());
    }
  }

  static hasProductTagParent(target) {
    return target.closest(`[${CHANGE_TYPE_ATTRIBUTE_KEY}="${TYPES.SMART_PRODUCT_TAG}"]`);
  }

  static getContent(change) {
    return { content: `<b>${change.settingVariable}</b> ${change.content}` };
  }
}
